<?xml version="1.0" encoding="UTF-8"?>
<!-- (c) ammap.com | SVG weather icons -->
<svg width="56" height="48" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="blur" x="-.20655" y="-.23099" width="1.403" height="1.5634">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="0" dy="4" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA slope="0.05" type="linear" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    <style type="text/css">
      <![CDATA[
      /*
** CLOUDS
*/
      @keyframes am-weather-cloud-2 {
        0% {
          -webkit-transform: translate(0px, 0px);
          -moz-transform: translate(0px, 0px);
          -ms-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
        }

        50% {
          -webkit-transform: translate(2px, 0px);
          -moz-transform: translate(2px, 0px);
          -ms-transform: translate(2px, 0px);
          transform: translate(2px, 0px);
        }

        100% {
          -webkit-transform: translate(0px, 0px);
          -moz-transform: translate(0px, 0px);
          -ms-transform: translate(0px, 0px);
          transform: translate(0px, 0px);
        }
      }

      .am-weather-cloud-2 {
        -webkit-animation-name: am-weather-cloud-2;
        -moz-animation-name: am-weather-cloud-2;
        animation-name: am-weather-cloud-2;
        -webkit-animation-duration: 3s;
        -moz-animation-duration: 3s;
        animation-duration: 3s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      /*
** SUN
*/
      @keyframes am-weather-sun {
        0% {
          -webkit-transform: rotate(0deg);
          -moz-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          -moz-transform: rotate(360deg);
          -ms-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      .am-weather-sun {
        -webkit-animation-name: am-weather-sun;
        -moz-animation-name: am-weather-sun;
        -ms-animation-name: am-weather-sun;
        animation-name: am-weather-sun;
        -webkit-animation-duration: 9s;
        -moz-animation-duration: 9s;
        -ms-animation-duration: 9s;
        animation-duration: 9s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      @keyframes am-weather-sun-shiny {
        0% {
          stroke-dasharray: 3px 10px;
          stroke-dashoffset: 0px;
        }

        50% {
          stroke-dasharray: 0.1px 10px;
          stroke-dashoffset: -1px;
        }

        100% {
          stroke-dasharray: 3px 10px;
          stroke-dashoffset: 0px;
        }
      }

      .am-weather-sun-shiny line {
        -webkit-animation-name: am-weather-sun-shiny;
        -moz-animation-name: am-weather-sun-shiny;
        -ms-animation-name: am-weather-sun-shiny;
        animation-name: am-weather-sun-shiny;
        -webkit-animation-duration: 2s;
        -moz-animation-duration: 2s;
        -ms-animation-duration: 2s;
        animation-duration: 2s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      /*
** SNOW
*/
      @keyframes am-weather-snow {
        0% {
          -webkit-transform: translateX(0) translateY(0);
          -moz-transform: translateX(0) translateY(0);
          -ms-transform: translateX(0) translateY(0);
          transform: translateX(0) translateY(0);
        }

        33.33% {
          -webkit-transform: translateX(-1.2px) translateY(2px);
          -moz-transform: translateX(-1.2px) translateY(2px);
          -ms-transform: translateX(-1.2px) translateY(2px);
          transform: translateX(-1.2px) translateY(2px);
        }

        66.66% {
          -webkit-transform: translateX(1.4px) translateY(4px);
          -moz-transform: translateX(1.4px) translateY(4px);
          -ms-transform: translateX(1.4px) translateY(4px);
          transform: translateX(1.4px) translateY(4px);
          opacity: 1;
        }

        100% {
          -webkit-transform: translateX(-1.6px) translateY(6px);
          -moz-transform: translateX(-1.6px) translateY(6px);
          -ms-transform: translateX(-1.6px) translateY(6px);
          transform: translateX(-1.6px) translateY(6px);
          opacity: 0;
        }
      }

      .am-weather-snow-1 {
        -webkit-animation-name: am-weather-snow;
        -moz-animation-name: am-weather-snow;
        -ms-animation-name: am-weather-snow;
        animation-name: am-weather-snow;
        -webkit-animation-duration: 2s;
        -moz-animation-duration: 2s;
        -ms-animation-duration: 2s;
        animation-duration: 2s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }
      ]]>
    </style>
  </defs>
  <g transform="translate(16,-2)" filter="url(#blur)">
    <g transform="translate(0,16)">
      <g class="am-weather-sun" transform="translate(0,16)"
        style="-moz-animation-duration:9s;-moz-animation-iteration-count:infinite;-moz-animation-name:am-weather-sun;-moz-animation-timing-function:linear;-ms-animation-duration:9s;-ms-animation-iteration-count:infinite;-ms-animation-name:am-weather-sun;-ms-animation-timing-function:linear;-webkit-animation-duration:9s;-webkit-animation-iteration-count:infinite;-webkit-animation-name:am-weather-sun;-webkit-animation-timing-function:linear">
        <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round" stroke-width="2" />
        <g transform="rotate(45)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(90)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(135)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="scale(-1)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(225)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(-90)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(-45)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffa500" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <circle r="5" fill="#ffa500" stroke="#ffa500" stroke-width="2" />
      </g>
    </g>
    <g class="am-weather-cloud-3"
      style="-moz-animation-duration:3s;-moz-animation-iteration-count:infinite;-moz-animation-name:am-weather-cloud-2;-moz-animation-timing-function:linear;-webkit-animation-duration:3s;-webkit-animation-iteration-count:infinite;-webkit-animation-name:am-weather-cloud-2;-webkit-animation-timing-function:linear">
      <path transform="translate(-20,-11)"
        d="m47.7 35.4c0-4.6-3.7-8.2-8.2-8.2-1 0-1.9 0.2-2.8 0.5-0.3-3.4-3.1-6.2-6.6-6.2-3.7 0-6.7 3-6.7 6.7 0 0.8 0.2 1.6 0.4 2.3-0.3-0.1-0.7-0.1-1-0.1-3.7 0-6.7 3-6.7 6.7 0 3.6 2.9 6.6 6.5 6.7h17.2c4.4-0.5 7.9-4 7.9-8.4z"
        fill="#57a0ee" stroke="#fff" stroke-linejoin="round" stroke-width="1.2" />
    </g>
    <g class="am-weather-snow-1"
      style="-moz-animation-duration:2s;-moz-animation-iteration-count:infinite;-moz-animation-name:am-weather-snow;-moz-animation-timing-function:linear;-ms-animation-duration:2s;-ms-animation-iteration-count:infinite;-ms-animation-name:am-weather-snow;-ms-animation-timing-function:linear;-webkit-animation-duration:2s;-webkit-animation-iteration-count:infinite;-webkit-animation-name:am-weather-snow;-webkit-animation-timing-function:linear">
      <g transform="translate(12,28)" fill="none" stroke="#57a0ee" stroke-linecap="round">
        <line transform="translate(0,9)" y1="-2.5" y2="2.5" stroke-width="1.2" />
        <line transform="rotate(45,-10.864,4.5)" y1="-2.5" y2="2.5" />
        <line transform="rotate(90,-4.5,4.5)" y1="-2.5" y2="2.5" />
        <line transform="rotate(135,-1.864,4.5)" y1="-2.5" y2="2.5" />
      </g>
    </g>
  </g>
</svg>