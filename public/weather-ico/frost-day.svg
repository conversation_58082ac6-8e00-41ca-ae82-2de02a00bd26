<?xml version="1.0" encoding="UTF-8"?>
<!-- (c) ammap.com | SVG weather icons -->
<svg width="56" height="48" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="blur" x="-.20655" y="-.21122" width="1.403" height="1.4997">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="0" dy="4" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA slope="0.05" type="linear" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    <style type="text/css">
      <![CDATA[
      /*
** SUN
*/
      @keyframes am-weather-sun {
        0% {
          -webkit-transform: rotate(0deg);
          -moz-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          -moz-transform: rotate(360deg);
          -ms-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      .am-weather-sun {
        -webkit-animation-name: am-weather-sun;
        -moz-animation-name: am-weather-sun;
        -ms-animation-name: am-weather-sun;
        animation-name: am-weather-sun;
        -webkit-animation-duration: 9s;
        -moz-animation-duration: 9s;
        -ms-animation-duration: 9s;
        animation-duration: 9s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      /*
** FROST
*/
      @keyframes am-weather-frost {
        0% {
          -webkit-transform: translate(0.0px, 0.0px);
          -moz-transform: translate(0.0px, 0.0px);
          -ms-transform: translate(0.0px, 0.0px);
          transform: translate(0.0px, 0.0px);
        }

        1% {
          -webkit-transform: translate(0.3px, 0.0px);
          -moz-transform: translate(0.3px, 0.0px);
          -ms-transform: translate(0.3px, 0.0px);
          transform: translate(0.3px, 0.0px);
        }

        3% {
          -webkit-transform: translate(-0.3px, 0.0px);
          -moz-transform: translate(-0.3px, 0.0px);
          -ms-transform: translate(-0.3px, 0.0px);
          transform: translate(-0.3px, 0.0px);
        }

        5% {
          -webkit-transform: translate(0.3px, 0.0px);
          -moz-transform: translate(0.3px, 0.0px);
          -ms-transform: translate(0.3px, 0.0px);
          transform: translate(0.3px, 0.0px);
        }

        7% {
          -webkit-transform: translate(-0.3px, 0.0px);
          -moz-transform: translate(-0.3px, 0.0px);
          -ms-transform: translate(-0.3px, 0.0px);
          transform: translate(-0.3px, 0.0px);
        }

        9% {
          -webkit-transform: translate(0.3px, 0.0px);
          -moz-transform: translate(0.3px, 0.0px);
          -ms-transform: translate(0.3px, 0.0px);
          transform: translate(0.3px, 0.0px);
        }

        11% {
          -webkit-transform: translate(-0.3px, 0.0px);
          -moz-transform: translate(-0.3px, 0.0px);
          -ms-transform: translate(-0.3px, 0.0px);
          transform: translate(-0.3px, 0.0px);
        }

        13% {
          -webkit-transform: translate(0.3px, 0.0px);
          -moz-transform: translate(0.3px, 0.0px);
          -ms-transform: translate(0.3px, 0.0px);
          transform: translate(0.3px, 0.0px);
        }

        15% {
          -webkit-transform: translate(-0.3px, 0.0px);
          -moz-transform: translate(-0.3px, 0.0px);
          -ms-transform: translate(-0.3px, 0.0px);
          transform: translate(-0.3px, 0.0px);
        }

        16% {
          -webkit-transform: translate(0.0px, 0.0px);
          -moz-transform: translate(0.0px, 0.0px);
          -ms-transform: translate(0.0px, 0.0px);
          transform: translate(0.0px, 0.0px);
        }

        100% {
          -webkit-transform: translate(0.0px, 0.0px);
          -moz-transform: translate(0.0px, 0.0px);
          -ms-transform: translate(0.0px, 0.0px);
          transform: translate(0.0px, 0.0px);
        }
      }

      .am-weather-frost {
        -webkit-animation-name: am-weather-frost;
        -moz-animation-name: am-weather-frost;
        animation-name: am-weather-frost;
        -webkit-animation-duration: 1.11s;
        -moz-animation-duration: 1.11s;
        animation-duration: 1.11s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }
      ]]>
    </style>
  </defs>
  <g transform="translate(16,-2)" filter="url(#blur)">
    <g transform="translate(0,16)">
      <g class="am-weather-sun" transform="translate(0,16)">
        <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round" stroke-width="2" />
        <g transform="rotate(45)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(90)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(135)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="scale(-1)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />F
        </g>
        <g transform="rotate(225)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(-90)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(-45)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <circle r="5" fill="#ffc04a" stroke="#ffc04a" stroke-width="2" />
      </g>
    </g>
    <g transform="translate(-16,4)">
      <g class="am-weather-frost" stroke="#57a0ee" transform="translate(0,2)" fill="none" stroke-width="2"
        stroke-linecap="round"
        style="-moz-animation-duration:1.11s;-moz-animation-iteration-count:infinite;-moz-animation-name:am-weather-frost;-moz-animation-timing-function:linear;-webkit-animation-duration:1.11s;-webkit-animation-iteration-count:infinite;-webkit-animation-name:am-weather-frost;-webkit-animation-timing-function:linear">
        <path d="M11,32H45" />
        <path d="M15.5,37H40.5" />
        <path d="M22.5,42H33.5" />
      </g>
      <g>
        <path stroke="#57a0ee" transform="translate(0,0)" fill="none" stroke-width="2" stroke-linecap="round"
          d="M28,31V9M28,22l11,-3.67M34,20l2,-4M34,20l4,2M28,22l-11,-3.67M22,20l-2,-4M22,20l-4,2M28,14.27l3.01,-3.02M28,14.27l-3.01,-3.02" />
      </g>
    </g>
  </g>
</svg>