<?xml version="1.0" encoding="UTF-8"?>
<!-- (c) ammap.com | SVG weather icons -->
<svg width="56" height="48" version="1.1" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <filter id="blur" x="-.20655" y="-.21122" width="1.403" height="1.4997">
      <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
      <feOffset dx="0" dy="4" result="offsetblur" />
      <feComponentTransfer>
        <feFuncA slope="0.05" type="linear" />
      </feComponentTransfer>
      <feMerge>
        <feMergeNode />
        <feMergeNode in="SourceGraphic" />
      </feMerge>
    </filter>
    <style type="text/css">
      <![CDATA[
      /*
** SUN
*/
      @keyframes am-weather-sun {
        0% {
          -webkit-transform: rotate(0deg);
          -moz-transform: rotate(0deg);
          -ms-transform: rotate(0deg);
          transform: rotate(0deg);
        }

        100% {
          -webkit-transform: rotate(360deg);
          -moz-transform: rotate(360deg);
          -ms-transform: rotate(360deg);
          transform: rotate(360deg);
        }
      }

      .am-weather-sun {
        -webkit-animation-name: am-weather-sun;
        -moz-animation-name: am-weather-sun;
        -ms-animation-name: am-weather-sun;
        animation-name: am-weather-sun;
        -webkit-animation-duration: 9s;
        -moz-animation-duration: 9s;
        -ms-animation-duration: 9s;
        animation-duration: 9s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      /*
** FOG
*/
      @keyframes am-weather-fog-1 {
        0% {
          transform: translate(0px, 0px)
        }

        50% {
          transform: translate(7px, 0px)
        }

        100% {
          transform: translate(0px, 0px)
        }
      }

      .am-weather-fog-1 {
        -webkit-animation-name: am-weather-fog-1;
        -moz-animation-name: am-weather-fog-1;
        -ms-animation-name: am-weather-fog-1;
        animation-name: am-weather-fog-1;
        -webkit-animation-duration: 8s;
        -moz-animation-duration: 8s;
        -ms-animation-duration: 8s;
        animation-duration: 8s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      @keyframes am-weather-fog-2 {
        0% {
          transform: translate(0px, 0px)
        }

        21.05% {
          transform: translate(-6px, 0px)
        }

        78.95% {
          transform: translate(9px, 0px)
        }

        100% {
          transform: translate(0px, 0px)
        }
      }

      .am-weather-fog-2 {
        -webkit-animation-name: am-weather-fog-2;
        -moz-animation-name: am-weather-fog-2;
        -ms-animation-name: am-weather-fog-2;
        animation-name: am-weather-fog-2;
        -webkit-animation-duration: 20s;
        -moz-animation-duration: 20s;
        -ms-animation-duration: 20s;
        animation-duration: 20s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      @keyframes am-weather-fog-3 {
        0% {
          transform: translate(0px, 0px)
        }

        25% {
          transform: translate(4px, 0px)
        }

        75% {
          transform: translate(-4px, 0px)
        }

        100% {
          transform: translate(0px, 0px)
        }
      }

      .am-weather-fog-3 {
        -webkit-animation-name: am-weather-fog-3;
        -moz-animation-name: am-weather-fog-3;
        -ms-animation-name: am-weather-fog-3;
        animation-name: am-weather-fog-3;
        -webkit-animation-duration: 6s;
        -moz-animation-duration: 6s;
        -ms-animation-duration: 6s;
        animation-duration: 6s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }

      @keyframes am-weather-fog-4 {
        0% {
          transform: translate(0px, 0px)
        }

        50% {
          transform: translate(-4px, 0px)
        }

        100% {
          transform: translate(0px, 0px)
        }
      }

      .am-weather-fog-4 {
        -webkit-animation-name: am-weather-fog-4;
        -moz-animation-name: am-weather-fog-4;
        -ms-animation-name: am-weather-fog-4;
        animation-name: am-weather-fog-4;
        -webkit-animation-duration: 6s;
        -moz-animation-duration: 6s;
        -ms-animation-duration: 6s;
        animation-duration: 6s;
        -webkit-animation-timing-function: linear;
        -moz-animation-timing-function: linear;
        -ms-animation-timing-function: linear;
        animation-timing-function: linear;
        -webkit-animation-iteration-count: infinite;
        -moz-animation-iteration-count: infinite;
        -ms-animation-iteration-count: infinite;
        animation-iteration-count: infinite;
      }
      ]]>
    </style>
  </defs>
  <g transform="translate(16,-2)" filter="url(#blur)">
    <g transform="translate(0,16)">
      <g class="am-weather-sun" transform="translate(0,16)">
        <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round" stroke-width="2" />
        <g transform="rotate(45)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(90)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(135)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="scale(-1)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />F
        </g>
        <g transform="rotate(225)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(-90)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <g transform="rotate(-45)">
          <line transform="translate(0,9)" y2="3" fill="none" stroke="#ffc04a" stroke-linecap="round"
            stroke-width="2" />
        </g>
        <circle r="5" fill="#ffc04a" stroke="#ffc04a" stroke-width="2" />
      </g>
    </g>
    <g class="am-weather-fog" transform="translate(-10,20)" fill="none" stroke="#c6deff" stroke-linecap="round"
      stroke-width="2">
      <line class="am-weather-fog-1" y1="0" y2="0" x1="1" x2="37" stroke-dasharray="3, 5, 17, 5, 7" />
      <line class="am-weather-fog-2" y1="5" y2="5" x1="9" x2="33" stroke-dasharray="11, 7, 15" />
      <line class="am-weather-fog-3" y1="10" y2="10" x1="5" x2="40" stroke-dasharray="11, 7, 3, 5, 9" />
      <line class="am-weather-fog-4" y1="15" y2="15" x1="7" x2="42" stroke-dasharray="13, 5, 9, 5, 3" />
    </g>
  </g>
</svg>