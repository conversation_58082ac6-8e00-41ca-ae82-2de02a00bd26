<?xml version="1.0" encoding="UTF-8"?>
<!-- (c) ammap.com | SVG weather icons -->
<!-- Severe Thunderstorm | Contributed by hsoJ95 on GitHub: https://github.com/hsoj95 -->
<svg width="56" height="48" version="1.1" xmlns="http://www.w3.org/2000/svg">
    <defs>
        <filter id="blur" x="-.17571" y="-.19575" width="1.3379" height="1.4959">
            <feGaussianBlur in="SourceAlpha" stdDeviation="3" />
            <feOffset dx="0" dy="4" result="offsetblur" />
            <feComponentTransfer>
                <feFuncA slope="0.05" type="linear" />
            </feComponentTransfer>
            <feMerge>
                <feMergeNode />
                <feMergeNode in="SourceGraphic" />
            </feMerge>
        </filter>
        <style type="text/css">
            <![CDATA[
            /*
** CLOUDS
*/
            @keyframes am-weather-cloud-3 {
                0% {
                    -webkit-transform: translate(-5px, 0px);
                    -moz-transform: translate(-5px, 0px);
                    -ms-transform: translate(-5px, 0px);
                    transform: translate(-5px, 0px);
                }

                50% {
                    -webkit-transform: translate(10px, 0px);
                    -moz-transform: translate(10px, 0px);
                    -ms-transform: translate(10px, 0px);
                    transform: translate(10px, 0px);
                }

                100% {
                    -webkit-transform: translate(-5px, 0px);
                    -moz-transform: translate(-5px, 0px);
                    -ms-transform: translate(-5px, 0px);
                    transform: translate(-5px, 0px);
                }
            }

            .am-weather-cloud-3 {
                -webkit-animation-name: am-weather-cloud-3;
                -moz-animation-name: am-weather-cloud-3;
                animation-name: am-weather-cloud-3;
                -webkit-animation-duration: 7s;
                -moz-animation-duration: 7s;
                animation-duration: 7s;
                -webkit-animation-timing-function: linear;
                -moz-animation-timing-function: linear;
                animation-timing-function: linear;
                -webkit-animation-iteration-count: infinite;
                -moz-animation-iteration-count: infinite;
                animation-iteration-count: infinite;
            }

            @keyframes am-weather-cloud-1 {
                0% {
                    -webkit-transform: translate(0px, 0px);
                    -moz-transform: translate(0px, 0px);
                    -ms-transform: translate(0px, 0px);
                    transform: translate(0px, 0px);
                }

                50% {
                    -webkit-transform: translate(2px, 0px);
                    -moz-transform: translate(2px, 0px);
                    -ms-transform: translate(2px, 0px);
                    transform: translate(2px, 0px);
                }

                100% {
                    -webkit-transform: translate(0px, 0px);
                    -moz-transform: translate(0px, 0px);
                    -ms-transform: translate(0px, 0px);
                    transform: translate(0px, 0px);
                }
            }

            .am-weather-cloud-1 {
                -webkit-animation-name: am-weather-cloud-1;
                -moz-animation-name: am-weather-cloud-1;
                animation-name: am-weather-cloud-1;
                -webkit-animation-duration: 3s;
                -moz-animation-duration: 3s;
                animation-duration: 3s;
                -webkit-animation-timing-function: linear;
                -moz-animation-timing-function: linear;
                animation-timing-function: linear;
                -webkit-animation-iteration-count: infinite;
                -moz-animation-iteration-count: infinite;
                animation-iteration-count: infinite;
            }

            /*
** STROKE
*/

            @keyframes am-weather-stroke {
                0% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                2% {
                    -webkit-transform: translate(0.3px, 0.0px);
                    -moz-transform: translate(0.3px, 0.0px);
                    -ms-transform: translate(0.3px, 0.0px);
                    transform: translate(0.3px, 0.0px);
                }

                4% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                6% {
                    -webkit-transform: translate(0.5px, 0.4px);
                    -moz-transform: translate(0.5px, 0.4px);
                    -ms-transform: translate(0.5px, 0.4px);
                    transform: translate(0.5px, 0.4px);
                }

                8% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                10% {
                    -webkit-transform: translate(0.3px, 0.0px);
                    -moz-transform: translate(0.3px, 0.0px);
                    -ms-transform: translate(0.3px, 0.0px);
                    transform: translate(0.3px, 0.0px);
                }

                12% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                14% {
                    -webkit-transform: translate(0.3px, 0.0px);
                    -moz-transform: translate(0.3px, 0.0px);
                    -ms-transform: translate(0.3px, 0.0px);
                    transform: translate(0.3px, 0.0px);
                }

                16% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                18% {
                    -webkit-transform: translate(0.3px, 0.0px);
                    -moz-transform: translate(0.3px, 0.0px);
                    -ms-transform: translate(0.3px, 0.0px);
                    transform: translate(0.3px, 0.0px);
                }

                20% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                22% {
                    -webkit-transform: translate(1px, 0.0px);
                    -moz-transform: translate(1px, 0.0px);
                    -ms-transform: translate(1px, 0.0px);
                    transform: translate(1px, 0.0px);
                }

                24% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                26% {
                    -webkit-transform: translate(-1px, 0.0px);
                    -moz-transform: translate(-1px, 0.0px);
                    -ms-transform: translate(-1px, 0.0px);
                    transform: translate(-1px, 0.0px);
                }

                28% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                40% {
                    fill: orange;
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }

                65% {
                    fill: white;
                    -webkit-transform: translate(-1px, 5.0px);
                    -moz-transform: translate(-1px, 5.0px);
                    -ms-transform: translate(-1px, 5.0px);
                    transform: translate(-1px, 5.0px);
                }

                61% {
                    fill: orange;
                }

                100% {
                    -webkit-transform: translate(0.0px, 0.0px);
                    -moz-transform: translate(0.0px, 0.0px);
                    -ms-transform: translate(0.0px, 0.0px);
                    transform: translate(0.0px, 0.0px);
                }
            }

            .am-weather-stroke {
                -webkit-animation-name: am-weather-stroke;
                -moz-animation-name: am-weather-stroke;
                animation-name: am-weather-stroke;
                -webkit-animation-duration: 1.11s;
                -moz-animation-duration: 1.11s;
                animation-duration: 1.11s;
                -webkit-animation-timing-function: linear;
                -moz-animation-timing-function: linear;
                animation-timing-function: linear;
                -webkit-animation-iteration-count: infinite;
                -moz-animation-iteration-count: infinite;
                animation-iteration-count: infinite;
            }

            @keyframes error {
                0% {
                    fill: #cc0000;
                }

                50% {
                    fill: #ff0000;
                }

                100% {
                    fill: #cc0000;
                }
            }

            #Shape {
                -webkit-animation-name: error;
                -moz-animation-name: error;
                animation-name: error;
                -webkit-animation-duration: 1s;
                -moz-animation-duration: 1s;
                animation-duration: 1s;
                -webkit-animation-timing-function: linear;
                -moz-animation-timing-function: linear;
                animation-timing-function: linear;
                -webkit-animation-iteration-count: infinite;
                -moz-animation-iteration-count: infinite;
                animation-iteration-count: infinite;
            }
            ]]>
        </style>
    </defs>
    <g transform="translate(16,-2)" filter="url(#blur)">
        <g class="am-weather-cloud-1"
            style="-moz-animation-duration:7s;-moz-animation-iteration-count:infinite;-moz-animation-name:am-weather-cloud-1;-moz-animation-timing-function:linear;-webkit-animation-duration:7s;-webkit-animation-iteration-count:infinite;-webkit-animation-name:am-weather-cloud-1;-webkit-animation-timing-function:linear">
            <path transform="matrix(.6 0 0 .6 -10 -6)"
                d="m47.7 35.4c0-4.6-3.7-8.2-8.2-8.2-1 0-1.9 0.2-2.8 0.5-0.3-3.4-3.1-6.2-6.6-6.2-3.7 0-6.7 3-6.7 6.7 0 0.8 0.2 1.6 0.4 2.3-0.3-0.1-0.7-0.1-1-0.1-3.7 0-6.7 3-6.7 6.7 0 3.6 2.9 6.6 6.5 6.7h17.2c4.4-0.5 7.9-4 7.9-8.4z"
                fill="#666" stroke="#fff" stroke-linejoin="round" stroke-width="1.2" />
        </g>
        <g class="am-weather-cloud-3">
            <path transform="translate(-20,-11)"
                d="m47.7 35.4c0-4.6-3.7-8.2-8.2-8.2-1 0-1.9 0.2-2.8 0.5-0.3-3.4-3.1-6.2-6.6-6.2-3.7 0-6.7 3-6.7 6.7 0 0.8 0.2 1.6 0.4 2.3-0.3-0.1-0.7-0.1-1-0.1-3.7 0-6.7 3-6.7 6.7 0 3.6 2.9 6.6 6.5 6.7h17.2c4.4-0.5 7.9-4 7.9-8.4z"
                fill="#333" stroke="#fff" stroke-linejoin="round" stroke-width="1.2" />
        </g>
        <g transform="matrix(1.2,0,0,1.2,-4,28)">
            <polygon class="am-weather-stroke"
                points="11.1 6.9 14.3 -2.9 20.5 -2.9 16.4 4.3 20.3 4.3 11.5 14.6 14.9 6.9" fill="#ffa500" stroke="#fff"
                style="-moz-animation-duration:1.11s;-moz-animation-iteration-count:infinite;-moz-animation-name:am-weather-stroke;-moz-animation-timing-function:linear;-webkit-animation-duration:1.11s;-webkit-animation-iteration-count:infinite;-webkit-animation-name:am-weather-stroke;-webkit-animation-timing-function:linear" />
        </g>
        <g class="warning" transform="translate(20,30)">
            <path
                d="m7.7791 2.906-5.9912 10.117c-0.56283 0.95042-0.24862 2.1772 0.7018 2.74 0.30853 0.18271 0.66051 0.27911 1.0191 0.27911h11.982c1.1046 0 2-0.89543 2-2 0-0.35857-0.0964-0.71056-0.27911-1.0191l-5.9912-10.117c-0.56283-0.95042-1.7896-1.2646-2.74-0.7018-0.28918 0.17125-0.53055 0.41262-0.7018 0.7018z"
                fill="#c00" />
            <path d="m9.5 10.5v-5" stroke="#fff" stroke-linecap="round" stroke-width="1.5" />
            <circle cx="9.5" cy="13" r="1" fill="#fff" />
        </g>
    </g>
</svg>