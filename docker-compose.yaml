services:
  searxng:
    image: docker.io/searxng/searxng:latest
    volumes:
      - ./searxng:/etc/searxng:rw
    ports:
      - 4000:8080
    networks:
      - perplexica-network
    restart: unless-stopped

  app:
    image: itzcrazykns1337/perplexica:main
    build:
      context: .
      dockerfile: app.dockerfile
    environment:
      - SEARXNG_API_URL=http://searxng:8080
      - DATA_DIR=/home/<USER>
    ports:
      - 3000:3000
    networks:
      - perplexica-network
    volumes:
      - backend-dbstore:/home/<USER>/data
      - uploads:/home/<USER>/uploads
      - ./config.toml:/home/<USER>/config.toml
    restart: unless-stopped

networks:
  perplexica-network:

volumes:
  backend-dbstore:
  uploads:
