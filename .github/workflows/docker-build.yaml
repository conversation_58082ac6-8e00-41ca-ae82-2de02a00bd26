name: Build & Push Docker Images

on:
  push:
    branches:
      - master
  release:
    types: [published]

jobs:
  build-amd64:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          install: true

      - name: Log in to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract version from release tag
        if: github.event_name == 'release'
        id: version
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - name: Build and push AMD64 Docker image
        if: github.ref == 'refs/heads/master' && github.event_name == 'push'
        run: |
          DOCKERFILE=app.dockerfile
          IMAGE_NAME=perplexica
          docker buildx build --platform linux/amd64 \
            --cache-from=type=registry,ref=itzcrazykns1337/${IMAGE_NAME}:amd64 \
            --cache-to=type=inline \
            --provenance false \
            -f $DOCKERFILE \
            -t itzcrazykns1337/${IMAGE_NAME}:amd64 \
            --push .

      - name: Build and push AMD64 release Docker image
        if: github.event_name == 'release'
        run: |
          DOCKERFILE=app.dockerfile
          IMAGE_NAME=perplexica
          docker buildx build --platform linux/amd64 \
            --cache-from=type=registry,ref=itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}-amd64 \
            --cache-to=type=inline \
            --provenance false \
            -f $DOCKERFILE \
            -t itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}-amd64 \
            --push .

  build-arm64:
    runs-on: ubuntu-24.04-arm
    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          install: true

      - name: Log in to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract version from release tag
        if: github.event_name == 'release'
        id: version
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - name: Build and push ARM64 Docker image
        if: github.ref == 'refs/heads/master' && github.event_name == 'push'
        run: |
          DOCKERFILE=app.dockerfile
          IMAGE_NAME=perplexica
          docker buildx build --platform linux/arm64 \
            --cache-from=type=registry,ref=itzcrazykns1337/${IMAGE_NAME}:arm64 \
            --cache-to=type=inline \
            --provenance false \
            -f $DOCKERFILE \
            -t itzcrazykns1337/${IMAGE_NAME}:arm64 \
            --push .

      - name: Build and push ARM64 release Docker image
        if: github.event_name == 'release'
        run: |
          DOCKERFILE=app.dockerfile
          IMAGE_NAME=perplexica
          docker buildx build --platform linux/arm64 \
            --cache-from=type=registry,ref=itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}-arm64 \
            --cache-to=type=inline \
            --provenance false \
            -f $DOCKERFILE \
            -t itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}-arm64 \
            --push .

  manifest:
    needs: [build-amd64, build-arm64]
    runs-on: ubuntu-latest
    steps:
      - name: Log in to DockerHub
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_PASSWORD }}

      - name: Extract version from release tag
        if: github.event_name == 'release'
        id: version
        run: echo "RELEASE_VERSION=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - name: Create and push multi-arch manifest for main
        if: github.ref == 'refs/heads/master' && github.event_name == 'push'
        run: |
          IMAGE_NAME=perplexica
          docker manifest create itzcrazykns1337/${IMAGE_NAME}:main \
            --amend itzcrazykns1337/${IMAGE_NAME}:amd64 \
            --amend itzcrazykns1337/${IMAGE_NAME}:arm64
          docker manifest push itzcrazykns1337/${IMAGE_NAME}:main

      - name: Create and push multi-arch manifest for releases
        if: github.event_name == 'release'
        run: |
          IMAGE_NAME=perplexica
          docker manifest create itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }} \
            --amend itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}-amd64 \
            --amend itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}-arm64
          docker manifest push itzcrazykns1337/${IMAGE_NAME}:${{ env.RELEASE_VERSION }}
