{"name": "perplexica-frontend", "version": "1.11.0-rc2", "license": "MIT", "author": "ItzCrazyKns", "scripts": {"dev": "next dev", "build": "npm run db:push && next build", "start": "next start", "lint": "next lint", "format:write": "prettier . --write", "db:push": "drizzle-kit push"}, "dependencies": {"@headlessui/react": "^2.2.0", "@iarna/toml": "^2.2.5", "@icons-pack/react-simple-icons": "^12.3.0", "@langchain/anthropic": "^0.3.24", "@langchain/community": "^0.3.49", "@langchain/core": "^0.3.66", "@langchain/google-genai": "^0.2.15", "@langchain/groq": "^0.2.3", "@langchain/ollama": "^0.2.3", "@langchain/openai": "^0.6.2", "@langchain/textsplitters": "^0.1.0", "@tailwindcss/typography": "^0.5.12", "@xenova/transformers": "^2.17.2", "axios": "^1.8.3", "better-sqlite3": "^11.9.1", "clsx": "^2.1.0", "compute-cosine-similarity": "^1.1.0", "compute-dot": "^1.1.0", "drizzle-orm": "^0.40.1", "html-to-text": "^9.0.5", "jspdf": "^3.0.1", "langchain": "^0.3.30", "lucide-react": "^0.363.0", "mammoth": "^1.9.1", "markdown-to-jsx": "^7.7.2", "next": "^15.2.2", "next-themes": "^0.3.0", "pdf-parse": "^1.1.1", "react": "^18", "react-dom": "^18", "react-text-to-speech": "^0.14.5", "react-textarea-autosize": "^8.5.3", "sonner": "^1.4.41", "tailwind-merge": "^2.2.2", "winston": "^3.17.0", "yet-another-react-lightbox": "^3.17.2", "zod": "^3.22.4"}, "devDependencies": {"@types/better-sqlite3": "^7.6.12", "@types/html-to-text": "^9.0.4", "@types/jspdf": "^2.0.0", "@types/node": "^20", "@types/pdf-parse": "^1.1.4", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "drizzle-kit": "^0.30.5", "eslint": "^8", "eslint-config-next": "14.1.4", "postcss": "^8", "prettier": "^3.2.5", "tailwindcss": "^3.3.0", "typescript": "^5"}}